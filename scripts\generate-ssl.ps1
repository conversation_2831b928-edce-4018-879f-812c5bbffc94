# Generate Self-Signed SSL Certificates for Development
# WARNING: Only use for development/testing. Production should use proper CA certificates.

param(
    [string]$Domain = "cloud.szxbl.org",
    [string]$OutputDir = "ssl",
    [int]$ValidDays = 365
)

$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

Write-ColorOutput $Yellow "WARNING: This will generate SELF-SIGNED certificates for DEVELOPMENT use only!"
Write-ColorOutput $Yellow "For production, use proper CA-issued certificates."
Write-Host ""

$confirm = Read-Host "Do you want to continue? (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-ColorOutput $Blue "Operation cancelled."
    exit
}

# Check if OpenSSL is available
try {
    openssl version | Out-Null
    Write-ColorOutput $Green "✓ OpenSSL found"
}
catch {
    Write-ColorOutput $Red "OpenSSL not found. Please install OpenSSL or use WSL."
    Write-ColorOutput $Blue "You can install OpenSSL from: https://slproweb.com/products/Win32OpenSSL.html"
    exit 1
}

# Create output directory if it doesn't exist
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-ColorOutput $Green "Created directory: $OutputDir"
}

# Generate private key
Write-ColorOutput $Blue "Generating private key..."
$keyPath = Join-Path $OutputDir "$Domain.key"
openssl genrsa -out $keyPath 2048

if ($LASTEXITCODE -ne 0) {
    Write-ColorOutput $Red "Failed to generate private key"
    exit 1
}

# Create certificate configuration
$configPath = Join-Path $OutputDir "cert.conf"
$configContent = @"
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=State
L=City
O=Organization
OU=IT Department
CN=$Domain

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $Domain
DNS.2 = *.$Domain
DNS.3 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
"@

$configContent | Out-File -FilePath $configPath -Encoding ASCII

# Generate certificate
Write-ColorOutput $Blue "Generating certificate..."
$certPath = Join-Path $OutputDir "$Domain.crt"
openssl req -new -x509 -key $keyPath -out $certPath -days $ValidDays -config $configPath -extensions v3_req

if ($LASTEXITCODE -ne 0) {
    Write-ColorOutput $Red "Failed to generate certificate"
    exit 1
}

# Clean up config file
Remove-Item $configPath -Force

# Set proper permissions (Windows)
Write-ColorOutput $Blue "Setting file permissions..."
icacls $keyPath /inheritance:r /grant:r "$env:USERNAME:(R)" | Out-Null
icacls $certPath /inheritance:r /grant:r "$env:USERNAME:(R)" /grant "Users:(R)" | Out-Null

Write-ColorOutput $Green "✓ SSL certificates generated successfully!"
Write-Host ""
Write-ColorOutput $Blue "Generated files:"
Write-Host "  Certificate: $certPath"
Write-Host "  Private Key: $keyPath"
Write-Host ""
Write-ColorOutput $Yellow "Next steps:"
Write-Host "1. These are SELF-SIGNED certificates - browsers will show security warnings"
Write-Host "2. For production, replace with proper CA-issued certificates"
Write-Host "3. You can now start the Docker services with: .\scripts\setup.ps1 -Start"
Write-Host ""

# Verify the certificate
Write-ColorOutput $Blue "Certificate details:"
openssl x509 -in $certPath -text -noout | Select-String "Subject:|DNS:|IP Address:|Not Before|Not After"
