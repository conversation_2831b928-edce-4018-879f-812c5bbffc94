# Extract SSL certificates from FRP configuration
# This script helps locate and copy certificates from FRP setup

param(
    [string]$FrpConfigPath = "",
    [switch]$Help
)

$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

if ($Help) {
    Write-ColorOutput $Blue "FRP SSL Certificate Extraction Helper"
    Write-Host ""
    Write-ColorOutput $Yellow "This script helps you locate and copy SSL certificates from your FRP configuration."
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\extract-from-frp.ps1 -FrpConfigPath 'C:\path\to\frp\config'"
    Write-Host ""
    Write-Host "Common FRP certificate locations:"
    Write-Host "  - FRP client config: frpc.ini or frpc.toml"
    Write-Host "  - Certificate files: usually in same directory as FRP config"
    Write-Host "  - Look for: tls_cert_file, tls_key_file, or similar settings"
    exit
}

Write-ColorOutput $Blue "FRP SSL Certificate Extraction Helper"
Write-Host ""

# Common FRP configuration file names
$frpConfigFiles = @("frpc.ini", "frpc.toml", "frpc.yaml", "frpc.yml")

# Search for FRP configuration files
if (-not $FrpConfigPath) {
    Write-ColorOutput $Yellow "Searching for FRP configuration files..."
    
    # Search in common locations
    $searchPaths = @(
        "C:\frp",
        "C:\Program Files\frp",
        "C:\tools\frp",
        "$env:USERPROFILE\frp",
        "$env:USERPROFILE\Desktop\frp",
        "$env:USERPROFILE\Downloads\frp"
    )
    
    foreach ($path in $searchPaths) {
        if (Test-Path $path) {
            foreach ($configFile in $frpConfigFiles) {
                $fullPath = Join-Path $path $configFile
                if (Test-Path $fullPath) {
                    Write-ColorOutput $Green "Found FRP config: $fullPath"
                    $FrpConfigPath = $path
                    break
                }
            }
            if ($FrpConfigPath) { break }
        }
    }
}

if (-not $FrpConfigPath -or -not (Test-Path $FrpConfigPath)) {
    Write-ColorOutput $Red "FRP configuration path not found or invalid."
    Write-ColorOutput $Yellow "Please specify the path to your FRP configuration directory:"
    Write-Host "  .\extract-from-frp.ps1 -FrpConfigPath 'C:\path\to\frp'"
    Write-Host ""
    Write-ColorOutput $Blue "Or use -Help for more information."
    exit 1
}

Write-ColorOutput $Blue "Searching for certificates in: $FrpConfigPath"

# Look for certificate files
$certExtensions = @("*.crt", "*.cert", "*.pem", "*.cer")
$keyExtensions = @("*.key", "*.pem")

$foundCerts = @()
$foundKeys = @()

foreach ($ext in $certExtensions) {
    $foundCerts += Get-ChildItem -Path $FrpConfigPath -Filter $ext -Recurse -ErrorAction SilentlyContinue
}

foreach ($ext in $keyExtensions) {
    $foundKeys += Get-ChildItem -Path $FrpConfigPath -Filter $ext -Recurse -ErrorAction SilentlyContinue
}

if ($foundCerts.Count -eq 0 -and $foundKeys.Count -eq 0) {
    Write-ColorOutput $Yellow "No certificate files found in the specified directory."
    Write-Host ""
    Write-ColorOutput $Blue "Manual steps:"
    Write-Host "1. Check your FRP configuration file for certificate paths"
    Write-Host "2. Look for settings like 'tls_cert_file' or 'tls_key_file'"
    Write-Host "3. Copy the certificate files to the ssl/ directory"
    Write-Host "4. Rename them to 'cloud.szxbl.org.crt' and 'cloud.szxbl.org.key'"
    exit
}

Write-ColorOutput $Green "Found certificate files:"
foreach ($cert in $foundCerts) {
    Write-Host "  Certificate: $($cert.FullName)"
}
foreach ($key in $foundKeys) {
    Write-Host "  Key: $($key.FullName)"
}

Write-Host ""
$copy = Read-Host "Do you want to copy these files to the ssl/ directory? (y/N)"

if ($copy -eq "y" -or $copy -eq "Y") {
    # Create ssl directory if it doesn't exist
    if (-not (Test-Path "ssl")) {
        New-Item -ItemType Directory -Path "ssl" -Force | Out-Null
    }
    
    # Copy certificate files
    if ($foundCerts.Count -gt 0) {
        $certFile = $foundCerts[0]  # Use first found certificate
        Copy-Item $certFile.FullName "ssl\cloud.szxbl.org.crt"
        Write-ColorOutput $Green "✓ Copied certificate to ssl\cloud.szxbl.org.crt"
    }
    
    if ($foundKeys.Count -gt 0) {
        $keyFile = $foundKeys[0]  # Use first found key
        Copy-Item $keyFile.FullName "ssl\cloud.szxbl.org.key"
        Write-ColorOutput $Green "✓ Copied private key to ssl\cloud.szxbl.org.key"
    }
    
    Write-Host ""
    Write-ColorOutput $Blue "Certificate files copied successfully!"
    Write-ColorOutput $Yellow "You can now start the services with: .\scripts\setup.ps1 -Start"
} else {
    Write-ColorOutput $Blue "Files not copied. You can manually copy them using:"
    if ($foundCerts.Count -gt 0) {
        Write-Host "  copy `"$($foundCerts[0].FullName)`" ssl\cloud.szxbl.org.crt"
    }
    if ($foundKeys.Count -gt 0) {
        Write-Host "  copy `"$($foundKeys[0].FullName)`" ssl\cloud.szxbl.org.key"
    }
}
