# Nextcloud with OnlyOffice Docker Setup

This Docker Compose setup provides a complete Nextcloud installation with OnlyOffice integration, PostgreSQL database, Redis caching, and Nginx reverse proxy with SSL support.

## Services Overview

- **Nextcloud**: Main cloud storage application (HTTPS on port 8081)
- **OnlyOffice**: Document editing server (HTTPS on port 8082)
- **PostgreSQL**: Database backend
- **Redis**: Caching layer
- **Nginx**: Reverse proxy with SSL termination

## Prerequisites

1. **Docker Desktop** installed on Windows 11
2. **SSL Certificates** for `cloud.szxbl.org` domain
3. **FRP Tunnel** configured to forward traffic from public server (**************)

## Quick Start

### 1. SSL Certificates Setup

Place your SSL certificates in the `ssl/` directory:
- `cloud.szxbl.org.crt` - SSL certificate
- `cloud.szxbl.org.key` - Private key

### 2. Environment Configuration

Copy and edit the environment file:
```bash
cp .env .env.local
```

Edit `.env.local` and set secure passwords:
```bash
# Generate secure passwords
POSTGRES_PASSWORD=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
NEXTCLOUD_ADMIN_PASSWORD=$(openssl rand -base64 32)
ONLYOFFICE_JWT_SECRET=$(openssl rand -base64 32)
```

### 3. Start Services

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Initial Setup

1. Access Nextcloud at: `https://cloud.szxbl.org`
2. Complete the initial setup wizard
3. Install OnlyOffice app from Nextcloud App Store
4. Configure OnlyOffice integration

## Configuration Details

### Nextcloud Configuration

The Nextcloud service is configured with:
- PostgreSQL database backend
- Redis caching
- Trusted domains: `cloud.szxbl.org`
- HTTPS protocol enforcement
- OnlyOffice document server integration

### OnlyOffice Configuration

OnlyOffice is configured with:
- JWT security enabled
- PostgreSQL database (shared with Nextcloud)
- Redis caching (shared with Nextcloud)
- SSL support

### Network Architecture

```
Internet → FRP Tunnel (**************) → Docker Host (Windows 11)
├── Port 8081 → Nginx → Nextcloud (HTTPS)
└── Port 8082 → Nginx → OnlyOffice (HTTPS)
```

## Security Features

- **SSL/TLS encryption** for all web traffic
- **JWT authentication** for OnlyOffice
- **Rate limiting** on login endpoints
- **Security headers** (HSTS, X-Frame-Options, etc.)
- **Isolated network** for service communication

## Maintenance

### Backup

```bash
# Backup volumes
docker-compose exec postgres pg_dump -U nextcloud nextcloud > backup.sql

# Backup Nextcloud data
docker cp nextcloud-app:/var/www/html ./nextcloud-backup
```

### Updates

```bash
# Update images
docker-compose pull

# Restart services
docker-compose down
docker-compose up -d
```

### Monitoring

```bash
# Check service health
docker-compose ps

# View resource usage
docker stats

# Check logs
docker-compose logs [service-name]
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   - Verify certificate files are in `ssl/` directory
   - Check certificate validity and key matching

2. **OnlyOffice Integration**
   - Ensure JWT secret matches between services
   - Check network connectivity between Nextcloud and OnlyOffice

3. **Database Connection**
   - Verify PostgreSQL is healthy
   - Check database credentials in environment variables

### Useful Commands

```bash
# Restart specific service
docker-compose restart nextcloud

# Access container shell
docker-compose exec nextcloud bash

# Check network connectivity
docker-compose exec nextcloud ping onlyoffice

# View detailed logs
docker-compose logs --tail=100 -f nextcloud
```

## Performance Tuning

### Resource Limits

Consider adding resource limits to docker-compose.yml:

```yaml
services:
  nextcloud:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

### PHP Configuration

For better performance, you can mount custom PHP configuration:

```yaml
volumes:
  - ./php/custom.ini:/usr/local/etc/php/conf.d/custom.ini
```

## Support

For issues specific to:
- **Nextcloud**: Check [Nextcloud Documentation](https://docs.nextcloud.com/)
- **OnlyOffice**: Check [OnlyOffice Documentation](https://api.onlyoffice.com/)
- **Docker**: Check [Docker Documentation](https://docs.docker.com/)

## License

This configuration is provided as-is for educational and production use.
