user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10G;
    client_body_buffer_size 400M;
    client_body_timeout 120s;
    client_header_timeout 120s;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Security headers
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;

    # Upstream definitions
    upstream nextcloud {
        server nextcloud:80;
        keepalive 32;
    }

    upstream onlyoffice {
        server onlyoffice:80;
        keepalive 32;
    }

    # Nextcloud Server (Port 443 -> 8081) - Handle both HTTP and HTTPS
    server {
        listen 443 ssl default_server;
        http2 on;
        server_name cloud.szxbl.org localhost _;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cloud.szxbl.org.crt;
        ssl_certificate_key /etc/nginx/ssl/cloud.szxbl.org.key;

        # Security headers specific to Nextcloud
        add_header Referrer-Policy "no-referrer" always;
        add_header X-Robots-Tag "noindex, nofollow" always;

        # Root and index
        location / {
            proxy_pass http://nextcloud;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # WebDAV and CalDAV support
            proxy_set_header Destination $http_destination;
            proxy_pass_header Authorization;

            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # Buffering
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            proxy_busy_buffers_size 16k;
        }

        # Nextcloud specific optimizations
        location = /.well-known/carddav {
            return 301 $scheme://$host/remote.php/dav;
        }

        location = /.well-known/caldav {
            return 301 $scheme://$host/remote.php/dav;
        }

        location = /.well-known/webfinger {
            return 301 $scheme://$host/index.php/.well-known/webfinger;
        }

        location = /.well-known/nodeinfo {
            return 301 $scheme://$host/index.php/.well-known/nodeinfo;
        }

        # Rate limiting for login
        location ~ ^/index\.php/login {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://nextcloud;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # OnlyOffice HTTPS Server (Port 8443 -> 8082)
    server {
        listen 8443 ssl;
        http2 on;
        server_name cloud.szxbl.org localhost;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cloud.szxbl.org.crt;
        ssl_certificate_key /etc/nginx/ssl/cloud.szxbl.org.key;

        # OnlyOffice specific headers
        add_header X-Content-Type-Options nosniff;

        location / {
            proxy_pass http://onlyoffice;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host:8082;

            # WebSocket support for OnlyOffice
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Timeouts for document editing
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;

            # Large file support
            client_max_body_size 100M;
        }

        # OnlyOffice health check
        location /healthcheck {
            proxy_pass http://onlyoffice/healthcheck;
            access_log off;
        }
    }


}
