version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: nextcloud-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-nextcloud}
      POSTGRES_USER: ${POSTGRES_USER:-nextcloud}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - nextcloud-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-nextcloud}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: nextcloud-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - nextcloud-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nextcloud Application
  nextcloud:
    image: nextcloud:28-apache
    container_name: nextcloud-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8081:80" # Direct access for testing
    environment:
      # Database Configuration
      POSTGRES_HOST: postgres
      POSTGRES_DB: ${POSTGRES_DB:-nextcloud}
      POSTGRES_USER: ${POSTGRES_USER:-nextcloud}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}

      # Redis Configuration
      REDIS_HOST: redis
      REDIS_HOST_PASSWORD: ${REDIS_PASSWORD}

      # Nextcloud Configuration
      NEXTCLOUD_ADMIN_USER: ${NEXTCLOUD_ADMIN_USER:-admin}
      NEXTCLOUD_ADMIN_PASSWORD: ${NEXTCLOUD_ADMIN_PASSWORD}
      NEXTCLOUD_TRUSTED_DOMAINS: ${NEXTCLOUD_TRUSTED_DOMAINS:-cloud.szxbl.org localhost}
      OVERWRITEPROTOCOL: https
      OVERWRITEHOST: cloud.szxbl.org
      OVERWRITECLIURL: https://cloud.szxbl.org

      # OnlyOffice Integration
      ONLYOFFICE_DOCUMENT_SERVER_URL: https://cloud.szxbl.org:8082

      # Performance and Security
      PHP_MEMORY_LIMIT: 1G
      PHP_UPLOAD_LIMIT: 10G
      APACHE_DISABLE_REWRITE_IP: 1
    volumes:
      - nextcloud_data:/var/www/html
      - nextcloud_config:/var/www/html/config
      - nextcloud_custom_apps:/var/www/html/custom_apps
      - nextcloud_themes:/var/www/html/themes
    networks:
      - nextcloud-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/status.php"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OnlyOffice Document Server
  onlyoffice:
    image: onlyoffice/documentserver:latest
    container_name: nextcloud-onlyoffice
    restart: unless-stopped
    environment:
      # JWT Security
      JWT_ENABLED: true
      JWT_SECRET: ${ONLYOFFICE_JWT_SECRET}
      JWT_HEADER: Authorization
      JWT_IN_BODY: true

      # Database Configuration (optional, uses internal SQLite by default)
      DB_TYPE: postgres
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${ONLYOFFICE_DB_NAME:-onlyoffice}
      DB_USER: ${POSTGRES_USER:-nextcloud}
      DB_PWD: ${POSTGRES_PASSWORD}

      # Redis Configuration
      REDIS_SERVER_HOST: redis
      REDIS_SERVER_PORT: 6379
      REDIS_SERVER_PASS: ${REDIS_PASSWORD}

      # SSL Configuration
      SSL_CERTIFICATE_PATH: /var/www/onlyoffice/Data/certs/onlyoffice.crt
      SSL_KEY_PATH: /var/www/onlyoffice/Data/certs/onlyoffice.key
      SSL_DHPARAM_PATH: /var/www/onlyoffice/Data/certs/dhparam.pem
      SSL_VERIFY_PEER: false

      # Performance
      WOPI_ENABLED: true
      USE_UNAUTHORIZED_STORAGE: false
    volumes:
      - onlyoffice_data:/var/www/onlyoffice/Data
      - onlyoffice_logs:/var/log/onlyoffice
      - ./ssl:/var/www/onlyoffice/Data/certs:ro
    networks:
      - nextcloud-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: nextcloud-nginx
    restart: unless-stopped
    depends_on:
      - nextcloud
      - onlyoffice
    ports:
      # - "8081:443" # Nextcloud HTTPS
      - "8082:8443" # OnlyOffice HTTPS
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - nextcloud-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nextcloud_data:
    driver: local
  nextcloud_config:
    driver: local
  nextcloud_custom_apps:
    driver: local
  nextcloud_themes:
    driver: local
  onlyoffice_data:
    driver: local
  onlyoffice_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  nextcloud-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
