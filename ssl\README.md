# SSL Certificate Setup Guide

This directory should contain your SSL certificates for the domain `cloud.szxbl.org`.

## 🎯 Quick Setup Options

### Option 1: Copy from Your Public Server (Recommended)

Since your certificates are already on server **************:

```powershell
# Using SCP (if you have SSH access)
scp user@**************:/path/to/ssl/cloud.szxbl.org.crt ./ssl/
scp user@**************:/path/to/ssl/cloud.szxbl.org.key ./ssl/

# Or using WinSCP, FileZilla, or similar tools
```

### Option 2: Extract from FRP Configuration

```powershell
# Run the extraction helper script
..\scripts\extract-from-frp.ps1
```

### Option 3: Generate Self-Signed (Development Only)

```powershell
# Generate self-signed certificates for testing
..\scripts\generate-ssl.ps1
```

## 📁 Required Files

Place these files in this directory:

1. **cloud.szxbl.org.crt** - SSL certificate file
2. **cloud.szxbl.org.key** - Private key file
3. **dhparam.pem** - Di<PERSON>ie-<PERSON><PERSON> parameters (optional)

## 🔍 Common Certificate Locations

### Let's Encrypt

```
/etc/letsencrypt/live/cloud.szxbl.org/fullchain.pem  → cloud.szxbl.org.crt
/etc/letsencrypt/live/cloud.szxbl.org/privkey.pem    → cloud.szxbl.org.key
```

### Nginx Configuration

Check your nginx config on the public server:

```bash
grep -r "ssl_certificate" /etc/nginx/
```

### Apache Configuration

```bash
grep -r "SSLCertificateFile" /etc/apache2/
```

### FRP Configuration

Look for these settings in your FRP config:

```ini
tls_cert_file = /path/to/certificate.crt
tls_key_file = /path/to/private.key
```

## 🔒 Security & Permissions

### Windows Permissions

```powershell
# Set proper permissions
icacls cloud.szxbl.org.key /inheritance:r /grant:r "$env:USERNAME:(R)"
icacls cloud.szxbl.org.crt /inheritance:r /grant:r "$env:USERNAME:(R)" /grant "Users:(R)"
```

### Linux/macOS Permissions

```bash
chmod 644 cloud.szxbl.org.crt
chmod 600 cloud.szxbl.org.key
chmod 644 dhparam.pem
```

## ✅ Verification

### Test Certificate Validity

```powershell
# Check certificate details
openssl x509 -in cloud.szxbl.org.crt -text -noout

# Check private key
openssl rsa -in cloud.szxbl.org.key -check

# Verify certificate and key match
openssl x509 -noout -modulus -in cloud.szxbl.org.crt | openssl md5
openssl rsa -noout -modulus -in cloud.szxbl.org.key | openssl md5
```

The MD5 hashes should match if the certificate and key are a pair.

### Test SSL Connection

```powershell
# Test SSL connection (after starting services)
openssl s_client -connect cloud.szxbl.org:8081 -servername cloud.szxbl.org
```

## 🚨 Troubleshooting

### Certificate Not Found Error

- Ensure files are named exactly: `cloud.szxbl.org.crt` and `cloud.szxbl.org.key`
- Check file permissions
- Verify files are in the correct directory

### Certificate/Key Mismatch

- Use the verification commands above
- Ensure you have the correct certificate and private key pair

### Browser Security Warnings

- For self-signed certificates: This is expected, you can proceed anyway
- For CA certificates: Check certificate validity and chain

## 📞 Need Help?

1. **Run the setup script** for guided assistance:

   ```powershell
   ..\scripts\setup.ps1
   ```

2. **Check certificate extraction** from your existing setup:

   ```powershell
   ..\scripts\extract-from-frp.ps1 -Help
   ```

3. **Generate development certificates** if needed:
   ```powershell
   ..\scripts\generate-ssl.ps1
   ```
