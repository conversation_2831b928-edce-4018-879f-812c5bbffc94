# SSL Certificates

This directory should contain your SSL certificates for the domain `cloud.szxbl.org`.

## Required Files

Place the following files in this directory:

1. **cloud.szxbl.org.crt** - SSL certificate file
2. **cloud.szxbl.org.key** - Private key file
3. **dhparam.pem** - Diffie-<PERSON><PERSON> parameters (optional, for enhanced security)

## File Permissions

Ensure proper file permissions for security:

```bash
# Set proper permissions (Linux/macOS)
chmod 644 cloud.szxbl.org.crt
chmod 600 cloud.szxbl.org.key
chmod 644 dhparam.pem
```

## Generating DH Parameters (Optional)

If you want to generate <PERSON><PERSON><PERSON>-<PERSON><PERSON> parameters for enhanced security:

```bash
openssl dhparam -out dhparam.pem 2048
```

## Certificate Sources

Since you mentioned the SSL certificates are already configured on your public server (198.12.106.124), you should:

1. Copy the certificate files from your public server
2. Or obtain them from your certificate authority
3. Or use Let's Encrypt if you're managing certificates yourself

## Testing SSL Configuration

After placing the certificates, you can test the SSL configuration:

```bash
# Test SSL certificate
openssl x509 -in cloud.szxbl.org.crt -text -noout

# Test private key
openssl rsa -in cloud.szxbl.org.key -check

# Verify certificate and key match
openssl x509 -noout -modulus -in cloud.szxbl.org.crt | openssl md5
openssl rsa -noout -modulus -in cloud.szxbl.org.key | openssl md5
```

The MD5 hashes should match if the certificate and key are a pair.
