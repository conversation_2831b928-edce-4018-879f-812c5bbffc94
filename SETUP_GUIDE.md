# 🚀 Nextcloud with OnlyOffice Setup Guide

## ✅ Current Status

Your Docker services are running successfully:

- ✅ **Nextcloud**: https://localhost:8081
- ✅ **OnlyOffice**: https://localhost:8082
- ✅ **PostgreSQL**: Database backend
- ✅ **Redis**: Caching layer
- ✅ **Nginx**: Reverse proxy

## 🔧 Step 1: Complete Nextcloud Initial Setup

1. **Open Nextcloud**: https://localhost:8081
2. **Create Admin Account**:

   - Username: `admin`
   - Password: `V2csL8S/dU2/qd+mjg0/nORvEkr0dFRLvPzPnRywtuo=`

3. **Configure Database**:

   - Database Type: **PostgreSQL**
   - Database Name: `nextcloud`
   - Database Username: `nextcloud`
   - Database Password: `bAoSMWe7P/w5PVj0Ermv3IpzBleWNWSIEyBUFNa+1Pc=`
   - Database Host: `postgres:5432`

4. **Click "Finish Setup"**

## 📄 Step 2: Install OnlyOffice App

1. **Go to Apps**: Click the grid icon (top-right) → Apps
2. **Search for "OnlyOffice"**: In the app store
3. **Install**: Click "Download and enable" for "ONLYOFFICE"
4. **Wait for installation** to complete

## 🔗 Step 3: Configure OnlyOffice Integration

1. **Go to Settings**: Click your profile → Settings
2. **Navigate to**: Administration → ONLYOFFICE
3. **Configure Document Server**:

   - **Document Server URL**: `http://onlyoffice` (internal network)
   - **Secret Key (JWT)**: `2YR3hx+ZOr+E/TDSplmIA91jss9O7YKjM4l5l9HTaIU=`
   - **Advanced Settings**:
     - ✅ Enable JWT token
     - ❌ Use HTTPS: **Disable** (internal HTTP communication)
     - ❌ Verify SSL certificate: **Disable**

4. **Save Settings**

## 🧪 Step 4: Test Document Editing

1. **Create a new document**:

   - Go to Files
   - Click "+" → New Document
   - Choose Word, Excel, or PowerPoint

2. **Test editing**:
   - The document should open in OnlyOffice editor
   - Try typing and formatting
   - Save the document

## 🌐 Step 5: Configure for External Access

### FRP Tunnel Configuration

Your services will be accessible via:

- **Nextcloud**: `https://cloud.szxbl.org` (via FRP tunnel port 8081)
- **OnlyOffice**: `https://cloud.szxbl.org:8082` (via FRP tunnel port 8082)

### Update Nextcloud Trusted Domains

If accessing externally, ensure your domain is trusted:

1. **Edit config**: `docker-compose exec nextcloud nano config/config.php`
2. **Add domain** to trusted_domains array:
   ```php
   'trusted_domains' => [
     0 => 'localhost',
     1 => 'cloud.szxbl.org',
     2 => '**************',
   ],
   ```

## 🔧 Maintenance Commands

```powershell
# Check service status
.\scripts\setup.ps1 -Status

# View logs
.\scripts\setup.ps1 -Logs

# View specific service logs
.\scripts\setup.ps1 -Logs -Service nextcloud

# Restart services
docker-compose restart

# Stop services
.\scripts\setup.ps1 -Stop

# Start services
.\scripts\setup.ps1 -Start
```

## 🚨 Troubleshooting

### OnlyOffice Connection Issues

1. **Check OnlyOffice health**:

   ```powershell
   docker-compose logs onlyoffice
   ```

2. **Test OnlyOffice directly**:

   - Open: https://localhost:8082
   - Should show OnlyOffice welcome page

3. **Verify JWT secret** matches in both services

### SSL Certificate Issues

- For self-signed certificates, browsers will show warnings
- Click "Advanced" → "Proceed to localhost (unsafe)"
- For production, use proper CA-issued certificates

### Database Connection Issues

1. **Check PostgreSQL**:

   ```powershell
   docker-compose exec postgres psql -U nextcloud -d nextcloud -c "\dt"
   ```

2. **Check credentials** in .env.local file

## 📊 Performance Optimization

### Resource Monitoring

```powershell
# Check resource usage
docker stats

# Check disk usage
docker system df
```

### Backup Strategy

```powershell
# Backup database
docker-compose exec postgres pg_dump -U nextcloud nextcloud > backup.sql

# Backup Nextcloud data
docker cp nextcloud-app:/var/www/html ./nextcloud-backup
```

## 🎯 Success Indicators

✅ **Nextcloud loads** at https://localhost:8081
✅ **OnlyOffice loads** at https://localhost:8082
✅ **Document editing works** (create/edit documents)
✅ **File upload/download** works
✅ **User management** works
✅ **External access** via https://cloud.szxbl.org

## 📞 Support

If you encounter issues:

1. Check the logs: `.\scripts\setup.ps1 -Logs`
2. Verify all services are healthy: `.\scripts\setup.ps1 -Status`
3. Test individual components
4. Check network connectivity between services

Your Nextcloud with OnlyOffice setup is now ready for production use! 🎉
