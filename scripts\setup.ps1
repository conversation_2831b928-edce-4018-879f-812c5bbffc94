# Nextcloud with OnlyOffice Setup Script for Windows
# This script helps set up the environment and start the services

param(
    [switch]$GeneratePasswords,
    [switch]$Start,
    [switch]$Stop,
    [switch]$Status,
    [switch]$Logs,
    [string]$Service = ""
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Generate-SecurePassword {
    $bytes = New-Object byte[] 32
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [Convert]::ToBase64String($bytes)
}

function Generate-Passwords {
    Write-ColorOutput $Blue "Generating secure passwords..."

    $envContent = @"
# Database Configuration
POSTGRES_DB=nextcloud
POSTGRES_USER=nextcloud
POSTGRES_PASSWORD=$(Generate-SecurePassword)

# Redis Configuration
REDIS_PASSWORD=$(Generate-SecurePassword)

# Nextcloud Configuration
NEXTCLOUD_ADMIN_USER=admin
NEXTCLOUD_ADMIN_PASSWORD=$(Generate-SecurePassword)
NEXTCLOUD_TRUSTED_DOMAINS=cloud.szxbl.org,localhost,127.0.0.1

# OnlyOffice Configuration
ONLYOFFICE_JWT_SECRET=$(Generate-SecurePassword)
ONLYOFFICE_DB_NAME=onlyoffice

# SSL Configuration
SSL_CERT_PATH=./ssl/cloud.szxbl.org.crt
SSL_KEY_PATH=./ssl/cloud.szxbl.org.key

# Network Configuration
NEXTCLOUD_DOMAIN=cloud.szxbl.org
ONLYOFFICE_DOMAIN=cloud.szxbl.org
"@

    $envContent | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-ColorOutput $Green "Passwords generated and saved to .env.local"
    Write-ColorOutput $Yellow "Please review and update .env.local with your specific configuration"
}

function Test-Prerequisites {
    Write-ColorOutput $Blue "Checking prerequisites..."

    # Check Docker
    if (-not (Test-DockerRunning)) {
        Write-ColorOutput $Red "Docker is not running. Please start Docker Desktop."
        return $false
    }
    Write-ColorOutput $Green "✓ Docker is running"

    # Check SSL certificates
    if (-not (Test-Path "ssl/cloud.szxbl.org.crt") -or -not (Test-Path "ssl/cloud.szxbl.org.key")) {
        Write-ColorOutput $Red "SSL certificates not found in ssl/ directory"
        Write-ColorOutput $Yellow "Please place cloud.szxbl.org.crt and cloud.szxbl.org.key in the ssl/ directory"
        return $false
    }
    Write-ColorOutput $Green "✓ SSL certificates found"

    # Check environment file
    if (-not (Test-Path ".env") -and -not (Test-Path ".env.local")) {
        Write-ColorOutput $Red "Environment file not found"
        Write-ColorOutput $Yellow "Please create .env or .env.local file with configuration"
        return $false
    }
    Write-ColorOutput $Green "✓ Environment file found"

    return $true
}

function Start-Services {
    Write-ColorOutput $Blue "Starting Nextcloud services..."

    if (-not (Test-Prerequisites)) {
        Write-ColorOutput $Red "Prerequisites check failed. Please fix the issues above."
        return
    }

    # Use .env.local if it exists, otherwise use .env
    $envFile = if (Test-Path ".env.local") { ".env.local" } else { ".env" }

    try {
        docker-compose --env-file $envFile up -d
        Write-ColorOutput $Green "Services started successfully!"
        Write-ColorOutput $Yellow "Nextcloud will be available at: https://cloud.szxbl.org"
        Write-ColorOutput $Yellow "OnlyOffice will be available at: https://cloud.szxbl.org:8082"
        Write-ColorOutput $Blue "Run 'docker-compose logs -f' to view logs"
    }
    catch {
        Write-ColorOutput $Red "Failed to start services: $_"
    }
}

function Stop-Services {
    Write-ColorOutput $Blue "Stopping Nextcloud services..."
    try {
        docker-compose down
        Write-ColorOutput $Green "Services stopped successfully!"
    }
    catch {
        Write-ColorOutput $Red "Failed to stop services: $_"
    }
}

function Show-Status {
    Write-ColorOutput $Blue "Service Status:"
    try {
        docker-compose ps
    }
    catch {
        Write-ColorOutput $Red "Failed to get service status: $_"
    }
}

function Show-Logs {
    if ($Service) {
        Write-ColorOutput $Blue "Showing logs for service: $Service"
        docker-compose logs -f $Service
    } else {
        Write-ColorOutput $Blue "Showing logs for all services:"
        docker-compose logs -f
    }
}

# Main script logic
if ($GeneratePasswords) {
    Generate-Passwords
}
elseif ($Start) {
    Start-Services
}
elseif ($Stop) {
    Stop-Services
}
elseif ($Status) {
    Show-Status
}
elseif ($Logs) {
    Show-Logs
}
else {
    Write-ColorOutput $Blue "Nextcloud with OnlyOffice Setup Script"
    Write-ColorOutput $Yellow "Usage:"
    Write-Host "  .\setup.ps1 -GeneratePasswords  # Generate secure passwords"
    Write-Host "  .\setup.ps1 -Start              # Start all services"
    Write-Host "  .\setup.ps1 -Stop               # Stop all services"
    Write-Host "  .\setup.ps1 -Status             # Show service status"
    Write-Host "  .\setup.ps1 -Logs               # Show logs for all services"
    Write-Host "  .\setup.ps1 -Logs -Service nextcloud  # Show logs for specific service"
    Write-Host ""
    Write-ColorOutput $Blue "SSL Certificate Setup:"
    Write-Host "  .\generate-ssl.ps1              # Generate self-signed certificates (dev only)"
    Write-Host "  .\extract-from-frp.ps1          # Extract certificates from FRP setup"
    Write-Host ""

    # Quick status check
    if (Test-Path "ssl/cloud.szxbl.org.crt" -and Test-Path "ssl/cloud.szxbl.org.key") {
        Write-ColorOutput $Green "✓ SSL certificates are present"
    } else {
        Write-ColorOutput $Yellow "⚠ SSL certificates not found in ssl/ directory"
        Write-Host "  Run one of the SSL setup scripts above to configure certificates"
    }

    if (Test-Path ".env" -or Test-Path ".env.local") {
        Write-ColorOutput $Green "✓ Environment configuration found"
    } else {
        Write-ColorOutput $Yellow "⚠ Environment configuration not found"
        Write-Host "  Run: .\setup.ps1 -GeneratePasswords"
    }
}
